@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --breakpoint-xs: 32rem;
  --breakpoint-3xl: 128rem;
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --radius: 0.625rem;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(44.4% 0.177 26.899);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Hide Spin buttons from input type=number */

  /* For Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  /* For Firefox */
  input[type="number"] {
    appearance: textfield;
    -moz-appearance: textfield;
  }
}

@layer components {
  /* React Flow Dark Theme Overrides */

  /* Controls styling */
  .react-flow__controls {
    @apply bg-accent p-1 rounded-sm shadow-md border-border;
  }

  .react-flow__controls-button {
    @apply !bg-accent !text-foreground !border-0 rounded-sm transition-all duration-200 ease-in-out;
  }

  .react-flow__controls-button:hover {
    @apply !bg-background !text-foreground;
  }

  .react-flow__controls-button:disabled {
    @apply bg-muted text-muted-foreground opacity-50;
  }

  /* MiniMap styling */
  .react-flow__minimap {
    @apply bg-background border border-border rounded-md shadow-md;
  }

  .react-flow__minimap-mask {
    @apply bg-background/80;
  }

  .react-flow__minimap-node {
    @apply fill-muted stroke-border stroke-1;
  }

  /* Background styling */
  .react-flow__background {
    @apply bg-background;
  }

  /* Background dots styling */
  .react-flow__background-pattern {
    @apply fill-border opacity-40;
  }

  /* Dark theme background dots */
  .dark .react-flow__background-pattern {
    @apply fill-border opacity-60;
  }

  /* Edge styling - Light theme */
  .react-flow__edge-path {
    @apply stroke-muted-foreground stroke-2 opacity-80;
  }

  .react-flow__edge.selected .react-flow__edge-path {
    @apply stroke-primary stroke-4 opacity-100;
  }

  .react-flow__edge:hover .react-flow__edge-path {
    @apply stroke-primary opacity-100;
  }

  .react-flow__connectionline {
    @apply stroke-primary stroke-2 opacity-100;
    stroke-dasharray: 5, 5;
  }

  /* Edge styling - Dark theme */
  .dark .react-flow__edge-path {
    @apply stroke-muted-foreground stroke-2 opacity-90;
  }

  .dark .react-flow__edge.selected .react-flow__edge-path {
    @apply stroke-primary stroke-3 opacity-100;
  }

  .dark .react-flow__edge:hover .react-flow__edge-path {
    @apply stroke-primary opacity-100;
  }

  .dark .react-flow__connectionline {
    @apply stroke-primary stroke-2 opacity-100;
    stroke-dasharray: 5, 5;
  }

  /* Edge labels */
  .react-flow__edge-text {
    @apply fill-foreground text-xs;
  }

  .react-flow__edge-textbg {
    @apply fill-background stroke-border stroke-1;
  }

  /* Handle styling */
  .react-flow__handle {
    @apply bg-background border-2 border-primary size-5 rounded-full opacity-0 transition-all duration-200 ease-in-out z-10;
  }

  .react-flow__handle:hover {
    @apply bg-primary scale-110 opacity-100 shadow-md size-7;
  }

  .react-flow__handle-connecting {
    @apply bg-primary scale-125 opacity-100 shadow-lg;
  }

  /* Handle visibility on node hover - Show all handles */
  .react-flow__node:hover .react-flow__handle {
    @apply opacity-100 bg-background border-foreground;
  }

  /* Handle positioning and visibility */
  .react-flow__handle-top {
    @apply -top-2;
  }

  .react-flow__handle-bottom {
    @apply -bottom-2;
  }

  .react-flow__handle-left {
    @apply -left-2;
  }

  .react-flow__handle-right {
    @apply -right-2;
  }

  /* Handle spacing for multiple handles on same side */
  .react-flow__handle[id*="source"] {
    @apply translate-x-1;
  }

  .react-flow__handle[id*="target"] {
    @apply -translate-x-1;
  }

  /* Specific positioning for top/bottom handles */
  .react-flow__handle-top[id*="source"] {
    @apply -translate-y-1 translate-x-1;
  }

  .react-flow__handle-top[id*="target"] {
    @apply -translate-y-1 -translate-x-1;
  }

  .react-flow__handle-bottom[id*="source"] {
    @apply translate-y-1 translate-x-1;
  }

  .react-flow__handle-bottom[id*="target"] {
    @apply translate-y-1 -translate-x-1;
  }

  /* Enhanced handle visibility for better UX */
  .react-flow__node.selected .react-flow__handle {
    @apply opacity-100;
  }

  /* Handle styling for dark theme */
  .dark .react-flow__handle {
    @apply bg-background border-foreground;
  }

  .dark .react-flow__handle:hover {
    @apply bg-primary shadow-md size-7;
  }

  /* Selection box styling */
  .react-flow__selection {
    @apply bg-primary/10 border-2 border-primary rounded;
  }

  /* Node selection styling */
  .react-flow__node.selected > [data-slot="card"] {
    @apply rounded-xl border border-foreground;
  }

  /* Pane cursor styling for space key */
  .react-flow__pane.dragging {
    @apply cursor-grabbing;
  }

  /* Attribution styling */
  .react-flow__attribution {
    @apply !bg-muted !text-muted-foreground !text-[5px];
  }

  .react-flow__attribution a {
    @apply text-primary no-underline;
  }

  .react-flow__attribution a:hover {
    @apply underline;
  }

  /* Pane styling */
  .react-flow__pane {
    @apply cursor-default;
  }

  .react-flow__pane.selection {
    @apply cursor-crosshair;
  }

  /* Transform styling */
  .react-flow__transform {
    @apply bg-background;
  }

  /* Edge delete button styling */
  .react-flow__edge:hover .edge-delete-button-container button {
    @apply opacity-100;
  }

  .edge-delete-button-container button {
    @apply opacity-0;
  }

  .edge-delete-button-container button:hover {
    @apply opacity-100;
  }
}
