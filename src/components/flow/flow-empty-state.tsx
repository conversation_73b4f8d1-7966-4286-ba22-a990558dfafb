'use client';

import { FacebookIcon } from '@/components/icons/social/facebook';
import { InstagramIcon } from '@/components/icons/social/instagram';
import { LinkedInIcon } from '@/components/icons/social/linkedin';
import { TikTokIcon } from '@/components/icons/social/tiktok';
import { WebsiteIcon } from '@/components/icons/social/website';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import {
  MessageSquareIcon,
  MicIcon,
  PlayIcon,
  PlusIcon,
  UploadIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';

interface FlowEmptyStateProps {
  onAddNode: (type: string, position?: { x: number; y: number }) => void;
}

export function FlowEmptyState({ onAddNode }: FlowEmptyStateProps) {
  const [isDragOver, setIsDragOver] = useState(false);

  const primaryActions = [
    {
      id: 'chat',
      icon: MessageSquareIcon,
      label: 'AI Chat',
      nodeType: 'chat',
      variant: 'default' as const,
    },
    {
      id: 'text',
      icon: PlusIcon,
      label: 'Add Text',
      nodeType: 'chat', // Map to chat for now
      variant: 'outline' as const,
    },
    {
      id: 'voice',
      icon: MicIcon,
      label: 'Record Voice',
      nodeType: 'voiceRecorder',
      variant: 'outline' as const,
    },
    {
      id: 'upload',
      icon: UploadIcon,
      label: 'Upload Docs, Audio and Video',
      nodeType: 'image',
      variant: 'outline' as const,
    },
  ];

  const socialPlatforms = [
    {
      id: 'youtube',
      icon: PlayIcon,
      label: 'YouTube',
      nodeType: 'youtube',
    },
    {
      id: 'instagram',
      icon: InstagramIcon,
      label: 'Instagram',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'tiktok',
      icon: TikTokIcon,
      label: 'TikTok',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'linkedin',
      icon: LinkedInIcon,
      label: 'LinkedIn',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'facebook',
      icon: FacebookIcon,
      label: 'Facebook Ads',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'website',
      icon: WebsiteIcon,
      label: 'Website',
      nodeType: 'image', // Map to image for now
    },
  ];

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        const file = files[0];
        if (file) {
          const centerPosition = { x: 400, y: 300 };

          // Determine node type based on file type
          if (file.type.startsWith('image/')) {
            onAddNode('image', centerPosition);
          } else if (file.type.startsWith('audio/')) {
            onAddNode('voiceRecorder', centerPosition);
          } else if (file.type.startsWith('video/')) {
            onAddNode('youtube', centerPosition);
          } else {
            onAddNode('image', centerPosition); // Default to image for other files
          }
        }
      }
    },
    [onAddNode]
  );

  const handleActionClick = (nodeType: string) => {
    // Center the node in the viewport
    const centerPosition = { x: 400, y: 300 };
    onAddNode(nodeType, centerPosition);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center justify-center z-50"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div
        className={`text-center space-y-6 max-w-xl mx-auto bg-background/95 backdrop-blur-sm border rounded-2xl p-8 shadow-lg transition-all duration-200 ${
          isDragOver ? 'border-primary border-2 bg-primary/5' : 'border-border'
        }`}
      >
        {/* Main heading */}
        <motion.h1
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.5 }}
          className={`text-base font-medium transition-colors duration-200 ${
            isDragOver ? 'text-primary' : 'text-foreground'
          }`}
        >
          {isDragOver
            ? 'Drop your files here to create content'
            : 'Drag and drop files here or click a button to start creating content'}
        </motion.h1>

        {/* Primary action buttons */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="flex flex-wrap justify-center gap-2"
        >
          {primaryActions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant}
              size="sm"
              onClick={() => handleActionClick(action.nodeType)}
              className="flex items-center gap-1.5 h-9 px-4 text-sm"
            >
              <action.icon className="h-4 w-4" />
              {action.label}
            </Button>
          ))}
        </motion.div>

        {/* OR separator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="flex items-center gap-3"
        >
          <div className="flex-1 h-px bg-border" />
          <span className="text-xs text-muted-foreground font-medium">OR</span>
          <div className="flex-1 h-px bg-border" />
        </motion.div>

        {/* Secondary instruction */}
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-sm text-muted-foreground"
        >
          Use Ctrl/Cmd + V to paste social media content and websites
        </motion.p>

        {/* Social media platforms */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="flex flex-wrap justify-center gap-2"
        >
          {socialPlatforms.map((platform) => (
            <Button
              key={platform.id}
              variant="ghost"
              size="sm"
              onClick={() => handleActionClick(platform.nodeType)}
              className="flex items-center gap-2 h-10 px-4 border border-border/50 hover:border-border hover:bg-accent/50"
            >
              <platform.icon className="h-4 w-4" />
              <span className="text-xs font-medium">{platform.label}</span>
            </Button>
          ))}
        </motion.div>
      </div>
    </motion.div>
  );
}
