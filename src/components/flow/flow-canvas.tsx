'use client';

import {
  Background,
  BackgroundVariant,
  type Connection,
  Controls,
  type Edge,
  MiniMap,
  type Node,
  type NodeTypes,
  PanOnScrollMode,
  ReactFlow,
  ReactFlowProvider,
  SelectionMode,
  addEdge,
  useEdgesState,
  useNodesState,
  useReactFlow,
} from '@xyflow/react';
import { useCallback, useRef, useState } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';

import '@xyflow/react/dist/style.css';

import { DeletableEdge } from './edges/deletable-edge';
import { FlowEmptyState } from './flow-empty-state';
import { FlowToolbar } from './flow-toolbar';
import { ChatNode } from './nodes/chat-node';
import { ImageNode } from './nodes/image-node';
import { VoiceRecorderNode } from './nodes/voice-recorder-node';
import { YouTubeNode } from './nodes/youtube-node';

const nodeTypes = {
  youtube: YouTubeNode,
  voiceRecorder: VoiceRecorderNode,
  image: ImageNode,
  chat: ChatNode,
} satisfies NodeTypes;

const edgeTypes = {
  default: DeletableEdge,
};

const initialNodes: Node[] = [];
const initialEdges: Edge[] = [];

interface FlowCanvasProps {
  boardId: string;
}

export function FlowCanvas({ boardId: _boardId }: FlowCanvasProps) {
  // TODO: Use _boardId for saving/loading board state
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [isSpacePressed, setIsSpacePressed] = useState(false);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { screenToFlowPosition } = useReactFlow();

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Function to detect URL type and create appropriate node
  const detectUrlType = useCallback((url: string): string => {
    const lowerUrl = url.toLowerCase();

    if (lowerUrl.includes('youtube.com') || lowerUrl.includes('youtu.be')) {
      return 'youtube';
    }
    if (lowerUrl.includes('instagram.com')) {
      return 'image'; // Map to image for now
    }
    if (lowerUrl.includes('tiktok.com')) {
      return 'image'; // Map to image for now
    }
    if (lowerUrl.includes('linkedin.com')) {
      return 'image'; // Map to image for now
    }
    if (lowerUrl.includes('facebook.com')) {
      return 'image'; // Map to image for now
    }

    // Default to image for any other URL
    return 'image';
  }, []);

  useHotkeys(
    'space',
    (event) => {
      event.preventDefault();
      setIsSpacePressed(true);
    },
    {
      keydown: true,
      keyup: false,
    }
  );

  useHotkeys(
    'space',
    (event) => {
      event.preventDefault();
      setIsSpacePressed(false);
    },
    {
      keydown: false,
      keyup: true,
    }
  );

  const addNode = useCallback(
    (type: string, position?: { x: number; y: number }) => {
      // Only create nodes for types that exist in nodeTypes
      const validTypes = Object.keys(nodeTypes);
      if (!validTypes.includes(type)) {
        console.warn(`Node type "${type}" is not implemented yet`);
        return;
      }

      const newNode: Node = {
        id: `${Date.now()}`,
        type,
        position: position || {
          x: Math.random() * 400,
          y: Math.random() * 400,
        },
        data: { label: `${type} node` },
      };
      setNodes((nds) => [...nds, newNode]);
    },
    [setNodes]
  );

  // Handle paste functionality globally
  const handlePaste = useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText();
      if (text) {
        // Get center position of the viewport
        const centerPosition = { x: 400, y: 300 };

        // Improved URL regex
        const urlRegex =
          /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
        if (urlRegex.test(text.trim())) {
          const nodeType = detectUrlType(text.trim());
          addNode(nodeType, centerPosition);
        } else {
          // If it's not a URL, create a chat node with the text content
          addNode('chat', centerPosition);
        }
      }
    } catch (err) {
      console.warn('Failed to read clipboard:', err);
    }
  }, [detectUrlType, addNode]);

  // Set up global keyboard shortcut for paste
  useHotkeys(['ctrl+v', 'cmd+v'], handlePaste, {
    preventDefault: true,
    enableOnFormTags: false,
  });

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      const nodeType = event.dataTransfer.getData('application/reactflow');
      if (nodeType && reactFlowWrapper.current) {
        const reactFlowBounds =
          reactFlowWrapper.current.getBoundingClientRect();
        const position = screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });
        addNode(nodeType, position);
      }
    },
    [addNode, screenToFlowPosition]
  );

  return (
    <ReactFlowProvider>
      <div
        className="w-full h-full relative"
        ref={reactFlowWrapper}
        onDrop={onDrop}
        onDragOver={onDragOver}
      >
        <FlowToolbar onAddNode={addNode} />
        {nodes.length === 0 && <FlowEmptyState onAddNode={addNode} />}
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          nodesDraggable={true}
          nodesConnectable={true}
          elementsSelectable={true}
          selectionMode={SelectionMode.Partial}
          multiSelectionKeyCode={['Meta', 'Ctrl']}
          deleteKeyCode={['Backspace', 'Delete']}
          panOnScroll={true}
          panOnScrollMode={PanOnScrollMode.Free}
          panOnDrag={isSpacePressed}
          zoomOnScroll={false}
          zoomOnDoubleClick={false}
          preventScrolling={false}
        >
          <Background
            variant={BackgroundVariant.Dots}
            color="var(--foreground)"
            gap={20}
            size={1}
          />
          <Controls showZoom={true} showFitView={true} showInteractive={true} />
          <MiniMap
            zoomable
            pannable
            className="!bg-background !border-border"
            nodeColor={(node) => {
              switch (node.type) {
                case 'youtube':
                  return 'var(--destructive)';
                case 'voiceRecorder':
                  return 'var(--primary)';
                case 'image':
                  return 'var(--chart-2)';
                case 'chat':
                  return 'var(--muted)';
                default:
                  return 'var(--chart-4)';
              }
            }}
            maskColor="rgba(var(--background), 0.8)"
          />
        </ReactFlow>
      </div>
    </ReactFlowProvider>
  );
}
